::ng-deep .mat-drawer-inner-container {
  background-color: var(--primary);
  border-right: 3px solid grey;
}
.sidenav-container {
  height: 100vh;
  width: 100%;
}
.sidenav {
  width: 70px;
  background-color: var(--primary);
}
.sidenav-content {
  margin-top: 64px;
  padding: var(--spacing-md);
  overflow-x: hidden;
}
.mobile-overlay {
  position: fixed;
  top: 56px;
  left: 0;
  width: 100%;
  height: calc(100vh - 56px);
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-mobile-overlay);
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
}
.mobile-overlay.active {
  opacity: 1;
  visibility: visible;
}
@media (max-width: 767px) {
  .sidenav {
    width: 70px;
    position: fixed;
    z-index: var(--z-mobile-sidebar);
  }
  .sidenav-content {
    margin-top: 56px;
    padding: var(--spacing-sm);
    margin-left: 0;
  }
  ::ng-deep .mat-drawer-container {
    background-color: var(--background);
  }
  ::ng-deep .mat-drawer-backdrop {
    background-color: rgba(0, 0, 0, 0.6);
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .sidenav {
    width: 60px;
  }
  .sidenav-content {
    margin-left: 60px;
    padding: var(--spacing-md);
    margin-top: 64px;
  }
}
@media (min-width: 1024px) {
  .sidenav {
    width: 70px;
  }
  .sidenav-content {
    margin-left: 70px;
    padding: var(--spacing-lg);
    margin-top: 64px;
  }
}
