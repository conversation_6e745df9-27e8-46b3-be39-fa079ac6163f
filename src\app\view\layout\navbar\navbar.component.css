::ng-deep .mat-toolbar-row,
.mat-toolbar-single-row {
  position: fixed;
  padding: 1.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 1100; /* Reduced to avoid conflicts with sidebar */
  background-color: var(--white);
  width: 100%;
  top: 0;
  left: 0;
}
.logo-container {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.logo {
  width: 12.5rem;
  height: 3.125rem;
  padding: 4px;
}

.brand-text {
  font-size: var(--font-2xl);
  font-weight: 600;
  color: var(--primary);
  letter-spacing: 1px;
}
.menu-section {
  position: absolute;
  right: 100px;
  top: 8px;
}
.menu-section .quick-menu {
  display: inline-block;
  height: 100%;
}

.menu-section .icon-category {
  position: relative;
  top: 8px;
}

.menu-section .icon-category .account {
  font-size: 13px !important;
}

.menu-section .icon-category .account .menu-btn {
  font-size: 14px !important;
}

.menu-toggle {
  position: absolute;
  right: 15px;
}

.sidemenu-header {
  padding: 10px 12px;
}

.menu-icon {
  font-size: 18px;
}

.menu-text {
  top: -2px;
  left: 6px;
  font-size: 14px;
  position: relative;
}

.border-btn {
  border: 1px solid;
}

.user-details {
  position: relative;
  display: inline-block;
}

.user-details .user-avatar {
  width: 40px;
  height: 40px;
  background-color: var(--primary);
  border-radius: 50%;
  position: relative;
}

.user-avatar .username-f-letter {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  color: var(--white);
  font-size: 20px;
  font-weight: 600;
  text-transform: uppercase;
}

.user-details .user-info {
  display: inline-block;
  margin-left: 15px;
}

.user-info .username {
  position: relative;
  top: 10px;
  letter-spacing: 0.5px;
  text-align: left;
}

/* ===== MOBILE MENU TOGGLE ===== */

.mobile-menu-toggle {
  display: none;
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  z-index: 1200; /* Ensure it's above navbar but below sidebar */
  padding: 8px; /* Add touch-friendly padding */
}

.mobile-menu-toggle .hamburger {
  width: 24px;
  height: 18px;
  position: relative;
  transform: rotate(0deg);
  transition: 0.3s ease-in-out;
}

.mobile-menu-toggle .hamburger span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: var(--primary);
  border-radius: 2px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: 0.25s ease-in-out;
}

.mobile-menu-toggle .hamburger span:nth-child(1) {
  top: 0px;
}

.mobile-menu-toggle .hamburger span:nth-child(2) {
  top: 8px;
}

.mobile-menu-toggle .hamburger span:nth-child(3) {
  top: 16px;
}

.mobile-menu-toggle.active .hamburger span:nth-child(1) {
  top: 8px;
  transform: rotate(135deg);
}

.mobile-menu-toggle.active .hamburger span:nth-child(2) {
  opacity: 0;
  left: -60px;
}

.mobile-menu-toggle.active .hamburger span:nth-child(3) {
  top: 8px;
  transform: rotate(-135deg);
}

/* ===== RESPONSIVE NAVBAR ===== */

@media (max-width: 767px) {
  ::ng-deep .mat-toolbar-row,
  .mat-toolbar-single-row {
    padding: var(--spacing-md);
    height: 56px;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .menu-section {
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
  }

  .logo {
    width: 8rem;
    height: 2rem;
    margin-left: 40px; /* Space for hamburger menu */
  }

  .brand-text {
    margin-left: 40px; /* Space for hamburger menu */
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--primary);
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  ::ng-deep .mat-toolbar-row,
  .mat-toolbar-single-row {
    padding: var(--spacing-lg);
  }

  .logo {
    width: 10rem;
    height: 2.5rem;
  }

  .menu-section {
    right: 80px;
  }
}

@media (min-width: 1024px) {
  .menu-section {
    right: 100px;
  }
}

.user-info .role {
  position: relative;
  top: -6px;
  font-size: 12px;
  font-weight: 400;
  text-align: left;
}
