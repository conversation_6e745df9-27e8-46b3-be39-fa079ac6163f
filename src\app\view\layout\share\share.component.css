.sharecomponent {
  max-height: 500px;
  width: 750px;
  padding: 1rem;
}

@media (max-width: 767px) {
  .sharecomponent {
    width: 95vw;
    max-width: none;
    padding: var(--spacing-sm);
    max-height: 80vh;
    overflow-y: auto;
  }

  .title {
    font-size: var(--font-lg);
  }

  /* Make table responsive */
  table {
    font-size: var(--font-sm);
  }

  thead tr th {
    padding: var(--spacing-xs);
    font-size: var(--font-xs);
  }

  tbody tr td {
    padding: var(--spacing-xs);
    font-size: var(--font-xs);
  }
}
.title {
  font-size: 20px;
  font-weight: 600;
  text-align: center;
}
thead tr th {
  background-color: var(--primary) !important;
  color: var(--white);
}
.yes {
  background-color: green !important;
}
.no {
  background-color: red;
}
