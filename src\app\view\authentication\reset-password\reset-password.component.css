.signup-card {
  position: fixed;
  margin: auto;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 50%;
  height: fit-content;
  border-radius: 5px;
  box-shadow: 6px 6px 33px var(--shadow);
  padding: 1rem;
}
@media (max-width: 1023px) {
  .signup-card {
    width: 80%;
    margin: var(--spacing-md);
  }
}
@media (max-width: 767px) {
  .signup-card {
    width: 95%;
    height: auto;
    position: relative;
    margin: var(--spacing-sm);
    padding: var(--spacing-md);
    top: auto;
    bottom: auto;
    left: auto;
    right: auto;
  }
  .user-container {
    width: 3.5rem;
    height: 3.5rem;
    margin-bottom: var(--spacing-md);
  }
  .user-icon {
    font-size: 2.5rem;
    height: 2.5rem;
    width: 2.5rem;
  }
  .signup-form {
    top: 0;
    padding: var(--spacing-sm);
  }
  .mat-mdc-form-field {
    width: 100%;
    margin-bottom: var(--spacing-sm);
  }
  .mat-mdc-button {
    width: 100%;
    margin: var(--spacing-sm) 0;
    min-height: 48px;
  }
}
.user-container {
  width: 4.3rem;
  height: 4.3rem;
  border-radius: 50%;
  background-color: var(--primary);
  position: relative;
  margin: auto;
}
.user-icon {
  font-size: 3rem;
  height: 3rem;
  width: 3rem;
  color: var(--white);
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}
.signup-form {
  position: relative;
  top: 1.25rem;
  margin: auto;
  padding: 0.625rem;
}
.signup-btn-container {
  margin: 1rem;
  display: flex;
  justify-content: space-evenly;
}
.action-container {
  color: var(--dark);
  text-align: center;
}
