.project-container {
  padding: 1rem 3rem;
  display: flex;
  width: 100%;
  position: relative;
  flex-direction: row;
}
.mat-stepper-vertical {
  background-color: var(--background) !important;
}

@media (max-width: 767px) {
  .project-container {
    flex-direction: column;
    height: fit-content;
    padding: var(--spacing-sm);
  }
}

.leaflet-div-icon2 {
  background: red;
  border: 0.3125rem solid rgba(255, 255, 255, 0.5); /* 5px */
  color: blue;

  font-weight: 500;
  text-align: center;
  border-radius: 50%;
  line-height: 1.875rem; /* 30px */
}

.active-project {
  width: 100%;
  border-radius: 10px;
  background-color: #f2f2f2;
  height: 105px;
  cursor: pointer;
  border: none;
  padding: 10px;
  display: inline-block;
  color: #1b1b1b;
  box-shadow: 0 3px 6px var(--shadow);
}
.active-project .project-image {
  height: 5.5rem; /* 80px */
  height: 5.5rem; /* 80px */
  border-radius: 0.625rem; /* 10px */
  float: left;
}

@media only screen and (max-width: 700px) {
  .active-project .project-image {
    height: 7rem;
    margin-top: 0.1rem;
    width: 30%;
    height: 7rem;
    margin-top: 0.1rem;
    width: 30%;
  }
}

.active-project .project-active-details {
  float: left;
  padding-left: 1.875rem; /* 30px */
  width: 75%;
}

.project-active-details p {
  margin-top: 0;
  margin-bottom: 0.3125rem; /* 5px */
}

.project-card {
  width: 50%;
}

@media (max-width: 700px) {
  .project-card {
    width: 100%;
  }
}

.project-list {
  width: 70%;
  position: relative;
  flex: 1;
}

@media (max-width: 700px) {
  .project-list {
    width: 80%;
  }
}

.horizontal-card {
  background-color: var(--primary);
  height: 100%;
  width: 100%;
  border-radius: 1.25rem; /* 20px */
  border: none;
  padding: 5.625rem 1.25rem; /* 90px 20px */
  position: relative;
}

.horizontal-card-bg {
  background-color: var(--primary);
  height: 100%;
  width: 25%;
  border-radius: 1.25rem; /* 20px */
  border: none;
  padding: 15.625rem 1.25rem; /* 250px 20px */
  position: relative;
}

.horizontal-card-bg-null {
  background-color: var(--white);
  height: 100%;
  width: 60%;
  border-radius: 1.25rem; /* 20px */
  box-shadow: none;
  border: transparent;
  padding: 5.625rem 1.25rem; /* 90px 20px */
  position: relative;
}

.horizontal-card .title {
  font-size: 10rem;
  font-weight: 900;
  color: rgba(187, 187, 187, 0.719);
  letter-spacing: 0.125rem;
}

@media (max-width: 700px) {
  .horizontal-card .title {
    margin-bottom: 5rem;
    margin-top: -2rem;
    font-size: 7rem;
    margin-left: -2rem;
  }
}

.horizontal-card .project-details {
  position: absolute;
  bottom: 1.25rem; /* 20px */
  padding-right: 3.125rem; /* 50px */
  width: 100%;
}

.progress-percentage {
  position: absolute;
  right: 3.125rem; /* 50px */
  top: 2.1875rem; /* 35px */
}

.custom-progress {
  height: 0.5rem; /* 8px */
  border-radius: 1.25rem; /* 20px */
}

.project-details h5 {
  text-transform: uppercase;
  letter-spacing: 0.0625rem; /* 1px */

  font-size: 1.5625rem; /* 25px */
  font-weight: 600;
  color: #eee;
}

.project-details p {
  color: #eee;
  font-size: 0.875rem; /* 14px */
}

.horizontal-card .project-image .img {
  width: 18.75rem; /* 300px */
  height: auto;
  position: absolute;
  right: -9.375rem; /* -150px */
  bottom: 0;
  top: 0;
  margin: auto;
  z-index: 100;
}

.stepper {
  padding: 1.25rem; /* 20px */
  margin-left: 1.25rem; /* 20px */
}

.stepper .title {
  font-size: 1.5625rem; /* 25px */

  letter-spacing: 0.0625rem; /* 1px */
}

.title .icon {
  position: relative;
  top: 0.25rem; /* 4px */
}

.stepper .steps {
  margin: 1.25rem 0; /* 20px 0 */
}

@media (max-width: 700px) {
  .stepper .steps {
    margin-left: 2rem;
    width: 100%;
    margin-top: 0rem; /* 20px 0 */
  }
}
.steps .custom-btn {
  margin: 0.625rem 1.25rem; /* 10px 20px */
}

@media (max-width: 700px) {
  .steps .custom-btn {
    margin: 0.625rem 1.25rem;
    width: max-content;
    margin-top: 2rem; /* 10px 20px */
  }
}

.steps .custom-btn .icon {
  top: -0.125rem; /* -2px */

  font-size: 1.5625rem; /* 25px */
  position: relative;
}

.prev,
.next {
  position: absolute;
  z-index: 1000;
  top: 0;
  bottom: 0;
  margin: auto;
}

.prev {
  left: 1.625rem; /* 10px */
}

.next {
  right: 1.625rem; /* 10px */
}

.mat-button {
  border: none;
  color: var(--primary);
  padding: 0.125rem 1.625rem; /* 2px 26px */

  font-size: 0.875rem; /* 14px */
  text-align: center;
  margin: 0.25rem 0.125rem; /* 4px 2px */
  opacity: 1;
  transition: 0.3s;
}

.mat-button:hover {
  opacity: 1;
  background-color: var(--primary);
  border: none;
  color: var(--white);
  padding: 0.125rem 1.8125rem; /* 2px 29px */

  font-size: 0.875rem; /* 14px */
  text-align: center;
  margin: 0.25rem 0.125rem; /* 4px 2px */
  opacity: 0.6;
  transition: 0.3s;
}

.title {
  font-weight: 600;
  font-size: 1.8rem;
  padding: 0.8rem 1.5rem;
}

@media (max-width: 700px) {
  .title-imp {
    display: inline-block;
    width: 20rem;
    margin-left: 2px;
  }
}
.card {
  background-color: var(--white);
  border-radius: 10px;
  border: none;
  position: relative;
  margin-bottom: 20px;
  margin-top: 20px;
  box-shadow:
    0 0.46875rem 2.1875rem rgba(90, 97, 105, 0.1),
    0 0.9375rem 1.40625rem rgba(90, 97, 105, 0.1),
    0 0.25rem 0.53125rem rgba(90, 97, 105, 0.12),
    0 0.125rem 0.1875rem rgba(90, 97, 105, 0.1);

  flex: 0 0 33.333333%;
}

.header-text {
  font-weight: 600;
  font-size: 1.6rem;

  text-align: center;
  text-transform: uppercase;
}

.card2 {
  background-color: #f2f2f2;
  border-radius: 0.625rem;
  border: none;
  position: relative;
  margin-bottom: 1rem;
  margin-bottom: 1rem;
  box-shadow:
    0 0.46875rem 2.1875rem rgba(90, 97, 105, 0.1),
    0 0.9375rem 1.40625rem rgba(90, 97, 105, 0.1),
    0 0.25rem 0.53125rem rgba(90, 97, 105, 0.12),
    0 0.125rem 0.1875rem rgba(90, 97, 105, 0.1);
  height: 130px;
  margin-left: 1rem;
  border-left: solid 6px var(--primary);
}

@media (min-width: 768px) {
  .card2 {
    display: flex;
  }
}
@media (min-width: 768px) {
  .card1 {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    background-color: #f2f2f2;
  }
}
.stats-card {
  padding: 0.5rem;
  background-color: #f2f2f2;
  position: relative;
}

.box-margin {
  padding: 0;
  margin: 0;
}

.box-head {
  font-size: 1.3rem;
  font-weight: 500;
  margin: 1rem;
  width: 100%;
}

.box-text {
  font-size: 1.5rem;
  font-weight: 500;
  margin: 0.5rem 1rem;
  width: 100%;
}

.box-sm {
  font-size: 1rem;
  font-weight: 500;
  margin: 0 1rem;
  width: 100%;
  width: 100%;
}

.display-flex {
  display: flex;
  justify-content: space-evenly;
  padding: 2rem;
}
@media (max-width: 767px) {
  .display-flex {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    padding: var(--spacing-sm);
    margin-top: 60px; /* Account for mobile navbar */
  }

  .active-project {
    height: auto;
    min-height: 105px;
    margin-bottom: var(--spacing-sm);
  }

  .box-head {
    font-size: var(--font-lg);
    margin: var(--spacing-sm);
  }

  .box-text {
    font-size: var(--font-xl);
    margin: var(--spacing-xs) var(--spacing-sm);
  }

  .box-sm {
    font-size: var(--font-md);
    margin: 0 var(--spacing-sm);
  }

  .stats-container {
    margin-bottom: var(--spacing-md);
  }

  .project-container {
    margin: var(--spacing-sm) 0;
  }

  .prev,
  .next {
    position: relative;
    margin: var(--spacing-xs);
  }
}
.btn-place {
  position: absolute;
  right: 1rem;
  top: 50px;
}
@media (max-width: 767px) {
  .btn-place {
    position: relative;
    right: auto;
    top: auto;
    margin: var(--spacing-sm) 0;
    text-align: center;
  }
}
.stats-container {
  width: 100%;
}
