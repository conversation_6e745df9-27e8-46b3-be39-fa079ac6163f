import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-section',
  templateUrl: './section.component.html',
  styleUrls: ['./section.component.css'],
})
export class SectionComponent implements OnInit, OnDestroy {
  isWindowOpen = true;
  isMobile = false;
  isMobileSidebarOpen = false;
  sidenavMode: 'side' | 'over' = 'side';

  private breakpointSubscription: Subscription;

  constructor(private breakpointObserver: BreakpointObserver) {}

  ngOnInit(): void {
    this.breakpointSubscription = this.breakpointObserver
      .observe([Breakpoints.Handset])
      .subscribe(result => {
        this.isMobile = result.matches;
        this.sidenavMode = this.isMobile ? 'over' : 'side';
        this.isWindowOpen = !this.isMobile;

        if (!this.isMobile) {
          this.isMobileSidebarOpen = false;
        }
      });
  }

  ngOnDestroy(): void {
    if (this.breakpointSubscription) {
      this.breakpointSubscription.unsubscribe();
    }
  }

  onToggleSidebar(isOpen: boolean): void {
    if (this.isMobile) {
      this.isMobileSidebarOpen = isOpen;
    }
  }

  closeMobileSidebar(): void {
    if (this.isMobile) {
      this.isMobileSidebarOpen = false;
    }
  }
}
