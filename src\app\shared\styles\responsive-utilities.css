/* ===== RESPONSIVE UTILITIES ===== */
/* Centralized responsive utilities to reduce CSS duplication */

/* Z-Index Management */
:root {
  --z-navbar: 1100;
  --z-mobile-toggle: 1200;
  --z-mobile-overlay: 1250;
  --z-mobile-sidebar: 1300;
  --z-modal: 1400;
  --z-tooltip: 1500;
}

/* Layout Utilities */
.layout-container {
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.content-area {
  padding: var(--spacing-md);
  overflow-x: hidden;
}

/* Mobile-first content spacing */
@media (max-width: 767px) {
  .content-area {
    margin-top: 56px; /* Mobile navbar height */
    padding: var(--spacing-sm);
    margin-left: 0;
  }
}

/* Tablet content spacing */
@media (min-width: 768px) and (max-width: 1023px) {
  .content-area {
    margin-top: 64px; /* Desktop navbar height */
    margin-left: 60px; /* Tablet sidebar width */
    padding: var(--spacing-md);
  }
}

/* Desktop content spacing */
@media (min-width: 1024px) {
  .content-area {
    margin-top: 64px; /* Desktop navbar height */
    margin-left: 70px; /* Desktop sidebar width */
    padding: var(--spacing-lg);
  }
}

/* Navigation Utilities */
.nav-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: var(--z-navbar);
  background-color: var(--white);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.nav-mobile-height {
  height: 56px;
}

.nav-desktop-height {
  height: 64px;
}

@media (max-width: 767px) {
  .nav-fixed {
    height: 56px;
    padding: var(--spacing-md);
  }
}

@media (min-width: 768px) {
  .nav-fixed {
    height: 64px;
    padding: 1.5rem;
  }
}

/* Sidebar Utilities */
.sidebar-base {
  background-color: var(--primary);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.sidebar-desktop {
  width: 70px;
  position: fixed;
  left: 0;
  top: 64px;
  height: calc(100vh - 64px);
}

.sidebar-tablet {
  width: 60px;
}

.sidebar-mobile {
  position: fixed;
  top: 56px;
  left: -70px;
  height: calc(100vh - 56px);
  z-index: var(--z-mobile-sidebar);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
  transition: left 0.3s ease;
}

.sidebar-mobile.open {
  left: 0;
}

/* Mobile Overlay */
.mobile-overlay {
  position: fixed;
  top: 56px;
  left: 0;
  width: 100%;
  height: calc(100vh - 56px);
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-mobile-overlay);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.mobile-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Menu Toggle Button */
.menu-toggle-btn {
  display: none;
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  z-index: var(--z-mobile-toggle);
  padding: 8px;
  border-radius: 4px;
}

@media (max-width: 767px) {
  .menu-toggle-btn {
    display: block;
  }
}

/* Hamburger Animation */
.hamburger {
  width: 24px;
  height: 18px;
  position: relative;
  transform: rotate(0deg);
  transition: 0.3s ease-in-out;
}

.hamburger span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: var(--primary);
  border-radius: 2px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: 0.25s ease-in-out;
}

.hamburger span:nth-child(1) { top: 0px; }
.hamburger span:nth-child(2) { top: 8px; }
.hamburger span:nth-child(3) { top: 16px; }

.hamburger.active span:nth-child(1) {
  top: 8px;
  transform: rotate(135deg);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
  left: -60px;
}

.hamburger.active span:nth-child(3) {
  top: 8px;
  transform: rotate(-135deg);
}

/* Touch-friendly targets */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Card responsive utilities */
.card-responsive {
  margin: var(--spacing-sm);
  padding: var(--spacing-sm);
}

@media (min-width: 768px) {
  .card-responsive {
    margin: var(--spacing-md);
    padding: var(--spacing-md);
  }
}

@media (min-width: 1024px) {
  .card-responsive {
    margin: var(--spacing-lg);
    padding: var(--spacing-lg);
  }
}

/* Form responsive utilities */
.form-responsive {
  width: 100%;
}

@media (max-width: 767px) {
  .form-responsive input,
  .form-responsive select,
  .form-responsive textarea {
    font-size: 16px !important; /* Prevent zoom on iOS */
  }
}

/* Button responsive utilities */
.btn-responsive {
  min-height: 44px;
  min-width: 44px;
  padding: var(--spacing-sm) var(--spacing-md);
}

@media (max-width: 767px) {
  .btn-responsive {
    width: 100%;
    margin: var(--spacing-xs) 0;
  }
}

/* Grid responsive utilities */
.grid-auto {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .grid-auto-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-auto-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-auto-4 { grid-template-columns: repeat(4, 1fr); }
}

/* Flex responsive utilities */
.flex-mobile-column {
  display: flex;
}

@media (max-width: 767px) {
  .flex-mobile-column {
    flex-direction: column;
  }
}

.flex-mobile-wrap {
  display: flex;
}

@media (max-width: 767px) {
  .flex-mobile-wrap {
    flex-wrap: wrap;
  }
}

/* Text responsive utilities */
.text-mobile-center {
  text-align: left;
}

@media (max-width: 767px) {
  .text-mobile-center {
    text-align: center;
  }
}

/* Spacing responsive utilities */
.spacing-responsive {
  margin: var(--spacing-sm);
  padding: var(--spacing-sm);
}

@media (min-width: 768px) {
  .spacing-responsive {
    margin: var(--spacing-md);
    padding: var(--spacing-md);
  }
}

@media (min-width: 1024px) {
  .spacing-responsive {
    margin: var(--spacing-lg);
    padding: var(--spacing-lg);
  }
}
