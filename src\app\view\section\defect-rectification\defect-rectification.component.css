.wrapper {
  width: 450px;
  background: var(--white);
  border-radius: 5px;
}
.wrapper form {
  margin: 10px;
  height: 350px;
  width: 450px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  border-radius: 12px;
  border: 5px dashed #d3d3d3;
}
form p {
  font-size: 16px;
  margin-top: 15px;
}
section .row {
  margin-bottom: 10px;
  background: #e9f0ff;
  list-style: none;
  padding: 15px 20px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
section .row i {
  color: var(--primary);
  font-size: 30px;
}
section .details span {
  font-size: 14px;
}
.progress-area .row .content {
  width: 100%;
  margin-left: 15px;
}
.progress-area .details {
  display: flex;
  align-items: center;
  margin-bottom: 7px;
  justify-content: space-between;
}
.progress-area .content .progress-bar {
  height: 5px;
  width: 100%;
  margin-bottom: 4px;
  background: var(--white);
  border-radius: 30px;
}
.content .progress-bar .progress {
  height: 100%;
  width: 0%;
  background: var(--primary);
  border-radius: inherit;
}
.uploaded-area {
  max-height: 232px;
  overflow-y: scroll;
}
.uploaded-area.onprogress {
  max-height: 150px;
}
.uploaded-area::-webkit-scrollbar {
  width: 0;
}
.uploaded-area .row .content {
  display: flex;
  align-items: center;
}
.uploaded-area .row .details {
  display: flex;
  margin-left: 15px;
  flex-direction: column;
}
.uploaded-area .row .details .size {
  color: #404040;
  font-size: 11px;
}
.header-info {
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  z-index: 3000;
  position: fixed;
  width: 100%;
  margin: 20px 0px;
}
.icon-fix-cloud {
  font-size: 55px;
  height: 50px !important;
  width: 60px !important;
  padding: 2px !important;
  color: var(--primary);
  cursor: pointer;
}
@media (max-width: 700px) {
  .filter-options {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-direction: column-reverse;
    justify-content: space-evenly;
  }
  .actions {
    flex-direction: column;
  }
}
@media (max-width: 900px) {
  .table-view {
    overflow-x: auto;
  }
}
.filter-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
}
.icon-fix-description,
.icon-fix-download {
  font-size: 38px;
  height: 40px;
  width: 50px !important;
  color: var(--primary);
  cursor: pointer;
}
@media (max-width: 600px) {
  .dropdown-fix,
  .search-fix {
    width: 140px;
  }
  .icon-fix-description {
    position: relative;
    top: -78px;
    left: 28%;
  }
  .icon-fix-download {
    position: relative;
    top: -37px;
  }
  .search-fix {
    position: relative;
    top: -66px;
    left: 2px;
  }
  .header-info {
    flex-direction: column;
  }
}
.actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
}
.upload-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto;
  padding: 20px;
}
.upload-instructions {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: 146px;
}
.title {
  font-size: 26px !important;
  color: #708090;
  line-height: 16px;
  font-weight: 700;
}
.browse-button {
  border: 2px solid var(--primary);
  border-radius: 19px;
  padding: 5px 19px;
  font-weight: 700;
}
.file-info {
  color: gray;
  font-size: 12px;
}
.file-types {
  font-weight: 700;
  margin-left: 8px;
  line-height: 10px;
}
.truncate-text {
  display: inline-block;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
