<mat-sidenav-container class="layout-container">
  <navbar (toggleSidebar)="onToggleSidebar($event)"></navbar>

  <!-- Desktop/Tablet: Use Angular Material sidenav -->
  <mat-sidenav
    #sidenav
    [opened]="isWindowOpen"
    [mode]="sidenavMode"
    [fixedInViewport]="true"
    [fixedTopGap]="64"
    class="sidenav"
    *ngIf="!isMobile">
    <sidebar [isMobileOpen]="false"></sidebar>
  </mat-sidenav>

  <!-- Mobile: Use custom overlay sidebar -->
  <sidebar
    [isMobileOpen]="isMobileSidebarOpen"
    (closeMobile)="closeMobileSidebar()"
    *ngIf="isMobile"></sidebar>

  <!-- Mobile overlay -->
  <div
    class="mobile-overlay"
    [class.active]="isMobileSidebarOpen"
    (click)="closeMobileSidebar()"
    *ngIf="isMobile"></div>

  <mat-sidenav-content class="content-area">
    <router-outlet></router-outlet>
  </mat-sidenav-content>
</mat-sidenav-container>
