@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('assets/fonts/poppins/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('assets/fonts/poppins/pxiEyp8kv8JHgFVrJJfecg.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('assets/fonts/poppins/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('assets/fonts/poppins/pxiByp8kv8JHgFVrLEj6Z1xlFQ.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('assets/fonts/poppins/pxiByp8kv8JHgFVrLCz7Z1xlFQ.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url(assets/fonts/icons/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
}
:root {
  --primary: darkcyan;
  --lite: #282828;
  --white: #fff;
  --background: white;
  --label-color: lightcyan;
  --shadow: #b5b5b5;
  --mobile-max: 767px;
  --tablet-min: 768px;
  --tablet-max: 1023px;
  --desktop-min: 1024px;
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --font-xs: 0.75rem;
  --font-sm: 0.875rem;
  --font-md: 1rem;
  --font-lg: 1.125rem;
  --font-xl: 1.25rem;
  --font-2xl: 1.5rem;
  --font-3xl: 2rem;
}
*:not(mat-icon) {
  font-family: 'Poppins', sans-serif !important;
}
html,
body {
  height: 100%;
  margin: 0;
  background-color: var(--background);
}
::-webkit-scrollbar {
  width: 5px !important;
  height: 5px !important;
}
::-webkit-scrollbar-thumb {
  background: var(--primary) !important;
  border-radius: 5px !important;
}
::-webkit-scrollbar-thumb:hover {
  background: var(--primary) !important;
  transition-duration: 0.4s !important;
}
a {
  color: var(--primary);
  text-decoration: none;
}
.grecaptcha-badge {
  display: none;
}
.step-heading {
  font-size: 25px !important;
  font-weight: 500 !important;
  letter-spacing: 1px !important;
  text-transform: capitalize !important;
}
.action {
  display: flow-root !important;
}
.stepper-action-btn {
  background-color: var(--primary) !important;
  color: var(--light) !important;
}
.mat-tab-label {
  height: 48px !important;
  padding: 0 20px !important;
  cursor: pointer !important;
  box-sizing: border-box !important;
  opacity: 0.6 !important;
  min-width: 88px !important;
  text-align: center !important;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  position: relative;
  text-transform: capitalize !important;
}
.mdc-tab__text-label {
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
}
::ng-deep .mdc-tab__text-label {
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
}
.mdc-tab-indicator--active {
  background-color: #f2f2f2;
}
.my-full-screen-dialog {
  max-width: 100vw !important;
  box-sizing: border-box;
  overflow: hidden;
}
.my-full-screen-dialog .mat-dialog-container {
  border-radius: 0;
  width: 100vw;
  height: 100vh;
  min-height: 100vh;
  padding: 0;
  overflow: hidden;
  background-color: var(--white);
}
.p-tabview-title {
  text-transform: capitalize;
}
.leaflet-draw-toolbar {
  border: 2px solid var(--primary) !important;
  position: relative;
  right: 50px;
  top: 100px;
}
.leaflet-touch .leaflet-right .leaflet-draw-actions {
  right: 90px !important;
  top: 100px !important;
}
.mat-mdc-select-arrow-wrapper {
  padding: 10px;
}
::ng-deep .mat-select-panel-wrap {
  word-wrap: none;
  word-break: keep-all;
}
.mat-mdc-option .mdc-list-item__primary-text {
  color: black;
}
.mat-mdc-slide-toggle.mat-mdc-slide-toggle-checked {
  .mdc-switch:enabled .mdc-switch__track::after {
    background: darkcyan !important;
  }
  .mdc-switch:enabled .mdc-switch__handle::after {
    background-color: darkcyan !important;
  }
}
.mat-button-toggle-appearance-standard.mat-button-toggle-checked {
  background-color: darkcyan !important;
  color: white !important;
}
.mat-button-toggle-appearance-standard .mat-button-toggle-label-content {
  padding: 0 9px !important;
  line-height: 40px !important;
}
.mat-step-header {
  padding: 18px !important;
}
.mat-drawer-container {
  display: unset !important;
}
.mat-mdc-mini-fab {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  background-color: var(--primary) !important;
  box-shadow:
    0 3px 5px -1px rgba(0, 0, 0, 0.2),
    0 6px 10px 0 rgba(0, 0, 0, 0.14),
    0 1px 18px 0 rgba(0, 0, 0, 0.12);
  color: var(--mat-mdc-fab-color, inherit);
}
.mat-step-header {
  height: 50px !important;
  padding: 5px 10px !important;
}
.mat-sort-header-arrow,
.mat-step-header .mat-step-icon-selected,
.mat-step-header .mat-step-icon-state-done,
.mat-step-header .mat-step-icon-state-edit {
  background-color: var(--primary) !important;
  color: var(--white) !important;
}
.mdc-linear-progress__bar-inner {
  border-color: var(--lite) !important;
  border-color: var(--primary) !important;
}
.mat-mdc-tab:not(.mat-mdc-tab-disabled).mdc-tab--active .mdc-tab__text-label,
.mat-mdc-tab-link:not(.mat-mdc-tab-disabled).mdc-tab--active .mdc-tab__text-label {
  color: var(--primary) !important;
}
.mat-mdc-tab:not(.mat-mdc-tab-disabled) .mdc-tab-indicator__content--underline,
.mat-mdc-tab-link:not(.mat-mdc-tab-disabled) .mdc-tab-indicator__content--underline {
  border-color: var(--label-color) !important;
}
.mdc-tab-indicator--active {
  background-color: var(--primary);
}
.form-select:focus {
  border-color: var(--primary) !important;
}
.form-select:hover {
  border-color: var(--primary) !important;
}
.mat-option {
  background-color: white !important;
  color: black !important;
  transition: background-color 0.3s;
}
.mat-option:hover {
  background-color: rgb(0, 139, 139) !important;
  color: white !important;
}
.mdc-text-field--filled:not(.mdc-text-field--disabled) {
  background-color: white !important;
}
.mat-primary
  .mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled)
  .mdc-list-item__primary-text {
  color: var(--primary) !important;
}
.mat-focused {
  border-color: none !important;
}
.mdc-text-field .mdc-text-field__input {
  caret-color: var(--primary) !important;
}
.mdc-line-ripple::before,
.mdc-line-ripple::after {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  border-bottom-style: solid;
  border-bottom-color: var(--primary) !important;
  content: '';
}
.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-floating-label,
.mat-mdc-form-field.mat-focused.mat-primary .mat-mdc-select-arrow,
.mat-primary .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-minimal::after,
.mat-primary .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-minimal::after,
.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-minimal::after,
.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-minimal::after {
  color: var(--primary) !important;
}
.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,
.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate {
  background-color: var(--primary) !important;
}
.mdc-data-table__cell {
  text-transform: capitalize;
  padding: 0 !important;
  text-align: center !important;
}
.mdc-data-table__header-cell {
  font-weight: 800 !important;
  text-transform: uppercase;
  color: var(--white);
  background-color: var(--primary) !important;
  padding: 0 !important;
}
.mat-sort-header-container,
.mat-mdc-paginator-container {
  justify-content: center !important;
}
.mat-calendar-body-selected {
  background-color: var(--primary) !important;
}
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.mdc-circular-progress__indeterminate-circle-graphic {
  stroke: var(--primary) !important;
}
.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
  backdrop-filter: blur(5px);
}
.logo-container img {
  position: absolute;
  border-radius: 50%;
  height: 200px;
  width: 200px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.logo-container .spinner {
  width: 225px !important;
  height: 225px !important;
}
.mat-mdc-button {
  background-color: var(--primary) !important;
  color: #fff !important;
}
.form-check-input:checked {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}
.mat-mdc-tab-body-content {
  overflow: hidden !important;
}
.responsive-img {
  width: 100%;
  height: auto;
  max-height: 60vh;
}
.container-responsive {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}
@media (max-width: 767px) {
  .container-responsive {
    padding: 0 var(--spacing-sm);
  }
}
.flex-responsive {
  display: flex;
}
.flex-column-mobile {
  display: flex;
}
@media (max-width: 767px) {
  .flex-column-mobile {
    flex-direction: column;
  }
}
.flex-wrap-mobile {
  display: flex;
}
@media (max-width: 767px) {
  .flex-wrap-mobile {
    flex-wrap: wrap;
  }
}
.grid-responsive {
  display: grid;
  gap: var(--spacing-md);
}
@media (min-width: 768px) {
  .grid-responsive-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  .grid-responsive-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  .grid-responsive-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}
.p-responsive {
  padding: var(--spacing-sm);
}
@media (min-width: 768px) {
  .p-responsive {
    padding: var(--spacing-md);
  }
}
@media (min-width: 1024px) {
  .p-responsive {
    padding: var(--spacing-lg);
  }
}
.m-responsive {
  margin: var(--spacing-sm);
}
@media (min-width: 768px) {
  .m-responsive {
    margin: var(--spacing-md);
  }
}
.text-responsive {
  font-size: var(--font-sm);
}
@media (min-width: 768px) {
  .text-responsive {
    font-size: var(--font-md);
  }
}
.heading-responsive {
  font-size: var(--font-lg);
  line-height: 1.4;
}
@media (min-width: 768px) {
  .heading-responsive {
    font-size: var(--font-xl);
  }
}
@media (min-width: 1024px) {
  .heading-responsive {
    font-size: var(--font-2xl);
  }
}
.hide-mobile {
  display: block;
}
@media (max-width: 767px) {
  .hide-mobile {
    display: none !important;
  }
}
.show-mobile {
  display: none;
}
@media (max-width: 767px) {
  .show-mobile {
    display: block !important;
  }
}
.hide-tablet {
  display: block;
}
@media (min-width: 768px) and (max-width: 1023px) {
  .hide-tablet {
    display: none !important;
  }
}
.show-tablet {
  display: none;
}
@media (min-width: 768px) and (max-width: 1023px) {
  .show-tablet {
    display: block !important;
  }
}
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.scrollable-mobile {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
@media (max-width: 767px) {
  .scrollable-mobile {
    max-height: 60vh;
  }
}
@media (max-width: 767px) {
  html,
  body {
    overflow: hidden !important;
    width: 100%;
  }
  input,
  select,
  textarea {
    font-size: 16px !important;
  }
  button,
  .mat-mdc-button,
  .mat-mdc-icon-button {
    min-height: 44px !important;
    min-width: 44px !important;
  }
  img {
    max-width: 100%;
    height: auto;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .container-responsive {
    padding: 0 var(--spacing-lg);
  }
}
@media (min-width: 1024px) {
  .container-responsive {
    padding: 0 var(--spacing-xl);
  }
}
@media (max-width: 767px) {
  ::ng-deep .mat-mdc-dialog-container {
    max-width: 95vw !important;
    max-height: 90vh !important;
    margin: var(--spacing-sm) !important;
  }
  ::ng-deep .mat-mdc-dialog-content {
    padding: var(--spacing-sm) !important;
    font-size: var(--font-sm) !important;
  }
  ::ng-deep .mat-mdc-select-panel {
    max-width: calc(100vw - 20px) !important;
    max-height: 50vh !important;
  }
  ::ng-deep .mat-mdc-select-value {
    font-size: var(--font-sm) !important;
  }
  ::ng-deep .mat-mdc-card {
    padding: var(--spacing-sm) !important;
    margin: var(--spacing-xs) !important;
  }
  ::ng-deep .mat-mdc-card-header {
    padding: var(--spacing-sm) !important;
  }
  ::ng-deep .mat-mdc-card-content {
    padding: var(--spacing-sm) !important;
    font-size: var(--font-sm) !important;
  }
  ::ng-deep .mat-mdc-form-field {
    width: 100% !important;
    margin-bottom: var(--spacing-sm) !important;
  }
  ::ng-deep .mat-mdc-form-field .mat-mdc-form-field-infix {
    min-height: 48px !important;
  }
  ::ng-deep .mat-mdc-button,
  ::ng-deep .mat-mdc-raised-button,
  ::ng-deep .mat-mdc-outlined-button {
    min-height: 48px !important;
    min-width: 48px !important;
    font-size: var(--font-sm) !important;
  }
  ::ng-deep .mat-mdc-icon-button {
    width: 48px !important;
    height: 48px !important;
    padding: var(--spacing-xs) !important;
  }
  ::ng-deep .mat-mdc-menu-panel {
    max-width: calc(100vw - 20px) !important;
  }
  ::ng-deep .mat-mdc-menu-item {
    min-height: 48px !important;
    font-size: var(--font-sm) !important;
  }
  ::ng-deep .mat-toolbar {
    min-height: 56px !important;
    padding: 0 var(--spacing-sm) !important;
  }
  ::ng-deep .mat-drawer-content {
    margin-top: 56px !important;
  }
  ::ng-deep .mat-mdc-table {
    font-size: var(--font-sm) !important;
  }
  ::ng-deep .mat-mdc-header-cell,
  ::ng-deep .mat-mdc-cell {
    padding: var(--spacing-xs) !important;
    font-size: var(--font-xs) !important;
  }
  ::ng-deep .mat-mdc-tab-label {
    min-width: 0 !important;
    padding: 0 var(--spacing-sm) !important;
    font-size: var(--font-sm) !important;
  }
  ::ng-deep .mat-step-header {
    padding: var(--spacing-sm) !important;
  }
  ::ng-deep .mat-step-label {
    font-size: var(--font-sm) !important;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  ::ng-deep .mat-mdc-dialog-container {
    max-width: 80vw !important;
  }
  ::ng-deep .mat-mdc-card {
    padding: var(--spacing-md) !important;
  }
  ::ng-deep .mat-toolbar {
    padding: 0 var(--spacing-md) !important;
  }
}
.history-control {
  position: fixed;
  right: 180px;
  bottom: 5px;
  margin: 5px !important;
  display: inline-flex;
  border-radius: 5px;
  border: 2px solid var(--primary) !important;
}
.history-control a,
.history-control a:hover {
  font-size: 2rem !important;
  font-weight: 700;
  text-decoration: none !important;
  color: black !important;
}
.leaflet-control-scale {
  position: fixed;
  right: 70px;
  bottom: 5px;
  margin: 5px !important;
}
.leaflet-control-scale-line {
  box-sizing: border-box;
  background: var(--white);
  text-shadow: 1px 1px var(--white);
  font-size: 14px;
  font-weight: 600;
  border: solid 2px var(--primary) !important;
  border-radius: 5px;
}
@media print {
  .leaflet-control {
    display: none !important;
  }
}
.leaflet-container a {
  background-color: var(--white);
  font-weight: 700;
  color: var(--primary);
}
.leaflet-popup-content {
  margin: 5px !important;
}
.tooltip {
  opacity: 10 !important;
}
.leaflet-right .leaflet-control {
  margin-right: 64px !important;
}
#print-template {
  visibility: hidden;
  position: fixed;
}
@media print {
  body * {
    visibility: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  #print-template,
  #print-template * {
    visibility: visible !important;
  }
  #print-template {
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    padding: 5px !important;
    box-sizing: border-box !important;
    font-weight: 600 !important;
    background-color: white !important;
  }
  .print-header {
    flex: 0 0 auto !important;
    margin-bottom: 10px !important;
    border-bottom: 2px solid #333 !important;
    padding-bottom: 10px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
  }
  .header-content {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100%;
  }
  .logo {
    max-width: 75px;
    height: auto;
  }
  .text-content {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    margin-left: 10px;
  }
  .print-map-container {
    flex: 1 1 auto !important;
    position: relative !important;
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    border: 1px solid #ccc !important;
    page-break-inside: avoid !important;
    overflow: hidden !important;
  }
  #print-map {
    position: relative !important;
    width: 100% !important;
    height: 100% !important;
    background: #f9f9f9 !important;
  }
  .print-footer {
    flex: 0 0 auto !important;
    margin-top: 10px !important;
    padding-top: 10px !important;
    border-top: 2px solid #333 !important;
    text-align: center !important;
  }
  .leaflet-control-container,
  .leaflet-control,
  .leaflet-popup {
    display: none !important;
  }
  @page {
    size: A4 landscape !important;
    margin: 0.5cm;
  }
}
