<mat-toolbar class="nav-fixed">
  <button
    class="mobile-menu-toggle"
    [class.active]="isMobileMenuOpen"
    (click)="toggleMobileMenu()"
    aria-label="Toggle mobile menu">
    <div class="hamburger" [class.active]="isMobileMenuOpen">
      <span></span>
      <span></span>
      <span></span>
    </div>
  </button>
  <div class="logo-container">
    <img class="logo" src="assets/logo.png" alt="Carnot" *ngIf="false" />
    <span class="brand-text">Carnot</span>
  </div>
  <span class="menu-section hide-mobile" fxShow="true">
    <button mat-mini-fab [matMenuTriggerFor]="menu"><mat-icon>perm_identity</mat-icon></button>
    <mat-menu #menu="matMenu">
      <button mat-menu-item [routerLink]="['/app', 'my-profile']">
        <mat-icon>account_circle</mat-icon>
        My Profile
      </button>
      <button mat-menu-item [routerLink]="['/app', 'manage-users']" *appHasRole="'admin'">
        <mat-icon>supervised_user_circle</mat-icon>
        Manage users
      </button>
      <button mat-menu-item (click)="logout()">
        <mat-icon>logout</mat-icon>
        Logout
      </button>
    </mat-menu>
  </span>
  <span class="menu-section show-mobile">
    <button mat-icon-button [matMenuTriggerFor]="mobileMenu"><mat-icon>more_vert</mat-icon></button>
    <mat-menu #mobileMenu="matMenu">
      <button mat-menu-item [routerLink]="['/app', 'my-profile']">
        <mat-icon>account_circle</mat-icon>
        My Profile
      </button>
      <button mat-menu-item [routerLink]="['/app', 'manage-users']" *appHasRole="'admin'">
        <mat-icon>supervised_user_circle</mat-icon>
        Manage users
      </button>
      <button mat-menu-item (click)="logout()">
        <mat-icon>logout</mat-icon>
        Logout
      </button>
    </mat-menu>
  </span>
</mat-toolbar>
