import { Component, EventEmitter, Output } from '@angular/core';

@Component({
  selector: 'navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.css'],
})
export class NavbarComponent {
  @Output() toggleSidebar: EventEmitter<boolean> = new EventEmitter();

  isMobileMenuOpen = false;

  toggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
    this.toggleSidebar.emit(this.isMobileMenuOpen);
  }

  logout(): void {
    // Implement logout functionality
    console.log('Logout clicked');
  }
}
