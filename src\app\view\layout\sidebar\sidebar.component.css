.fix-bottom {
  list-style: none;
  position: fixed;
  bottom: 0;
  width: 70px;
  display: flex;
  flex-direction: column;
  height: 275px;
  justify-content: space-evenly;
  align-items: center;
}
.fix-top {
  position: relative;
  width: 70px;
  list-style: none;
  display: flex;
  flex-direction: column;
  height: 275px;
  justify-content: space-evenly;
  align-items: center;
}
.list-item {
  width: 60px;
  height: fit-content;
  padding: 5px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}
.list-item:hover {
  background-color: lightslategrey !important;
  cursor: pointer;
}
.fixed-top {
  position: relative;
}
.menu-active {
  border-left: 5px solid var(--primary);
  background-color: #0060fe1c;
  color: var(--primary) !important;
}
.extended-sidebar {
  width: 180px;
}
.menu-section {
  position: absolute;
  right: 30px;
}
.menu-section .icon-category {
  position: relative;
  top: 8px;
}
.menu-section .icon-category .account {
  font-size: 13px !important;
}
.menu-section .icon-category .account .menu-btn {
  font-size: 14px !important;
}
.menu-toggle {
  position: absolute;
  right: 15px;
}
.sidemenu-header {
  padding: 10px 12px;
}
.menu-icon {
  font-size: 25px;
  color: var(--white);
}
.menu-icon:hover {
  font-size: 27px;
  font-weight: 400;
}
.app-logo {
  margin-bottom: 3px;
  width: 60px;
}
.logo {
  width: 12.5rem;
  height: 3.125rem;
  padding: 4px;
}
.menu-text {
  font-size: 10px;
  font-weight: 600;
  text-decoration: none;
  color: var(--white);
  text-align: center;
}
.menu-text:hover {
  text-decoration: none;
  cursor: pointer;
}
a {
  text-decoration: none;
}
.border-btn {
  border: 1px solid;
}
.compact-menu-item {
  position: relative;
}
.compact-menu-icon {
  position: fixed;
  left: 30px;
  width: 20px;
  height: 20px;
  color: var(--white);
  margin: -14px;
}
::ng-deep .mat-drawer-side {
  border-right: solid 0 rgba(0, 0, 0, 0.12) !important;
  box-shadow: 0 3px 3px var(--shadow) !important;
  background-color: var(--nav) !important;
}
::ng-deep .mat-list-base .mat-subheader {
  color: var(--white);
}
#compact-sidebar {
  display: flex;
  transition: transform 0.3s ease;
}
#extended-sidebar {
  display: none;
}
@media (max-width: 767px) {
  #compact-sidebar {
    position: fixed;
    top: 56px;
    left: -70px;
    height: calc(100vh - 56px);
    z-index: var(--z-mobile-sidebar);
    background-color: var(--primary);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
    transition: left 0.3s ease;
  }
  #compact-sidebar.mobile-open {
    left: 0;
  }
  .fix-top {
    height: auto;
    min-height: 100vh;
    justify-content: flex-start;
    padding-top: var(--spacing-md);
  }
  .list-item {
    margin-bottom: var(--spacing-sm);
    width: 55px;
  }
  .menu-icon {
    font-size: 20px;
  }
  .menu-text {
    font-size: 9px;
  }
  .app-logo {
    width: 55px;
    margin-bottom: var(--spacing-sm);
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  #compact-sidebar {
    width: 60px;
  }
  .fix-top {
    width: 60px;
  }
  .list-item {
    width: 50px;
  }
  .app-logo {
    width: 50px;
  }
  .menu-icon {
    font-size: 22px;
  }
  .menu-text {
    font-size: 9px;
  }
}
@media (min-width: 1024px) {
  #compact-sidebar {
    width: 70px;
  }
}
.menu-name {
  font-size: 10px;
  padding: 2px;
  color: var(--white);
  list-style: none;
}
.menu-name1 {
  font-size: 10px;
  padding: 2px;
  color: var(--white);
  list-style: none;
  margin-left: 6px;
  position: relative;
  top: 2px;
}
