.exam {
  position: fixed;
  left: 5rem; /* 80px */
  top: 0.75rem; /* 12px */
  width: 8.4375rem; /* 135px */
  z-index: 1000;
  align-content: right;
}
@media (max-width: 700px) {
  .custom-class {
    height: 50rem;
    max-width: 100%;
  }
}
.matcard_db {
  width: 26.875rem; /* 430px */
  height: 7.5rem; /* 120px */
  background-color: rgb(243, 241, 241);
}

#body-row {
  margin-left: 0;
  margin-right: 0;
  display: flex;
}

.map-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 1.875rem; /* 30px */
}

.map-frame {
  height: 100%;
}

@media only screen and (max-width: 53.75rem) {
  /* 860px */
  .bottom-container {
    display: block !important;
    right: 0;
  }
}

#mapid {
  height: 100%;
}

.dashboard-container {
  flex: 1;
  padding: 1.25rem; /* 20px */
}

.dashboard-container .title {
  font-size: 1.5625rem; /* 25px */

  font-weight: 600;
  letter-spacing: 2px;
}

.dashboard-container .welcome- {
  width: 100%;
  padding: 1.25rem; /* 20px */
  background-color: #0060fe;
  margin: 1.25rem 0; /* 20px */
  border-radius: 15px;
  color: #e7e7e7;
}

.dashboard-container .welcome-card .title {
  font-size: 1.25rem; /* 20px */

  font-weight: 600;
  color: #f7f7f7;
  letter-spacing: 2px;
  margin-bottom: 0.625rem; /* 10px */
}

.dashboard-container .welcome-card p {
  font-size: 0.875rem; /* 14px */

  letter-spacing: 1px;
}

.dashboard-container .stats-container {
  padding: 0.9375rem 0; /* 15px */
}

.dashboard-container .stats-container .title {
  font-size: 1.125rem; /* 18px */

  letter-spacing: 1px;
}

.dashboard-container .stats-container .stats-card {
  padding: 1.25rem; /* 20px */
  border-radius: 10px;
  margin: 0.625rem 0; /* 10px */
  display: flex;
  box-shadow: 2px 2px #77777730;
}

@media (max-width: 767px) {
  .dash-stats {
    width: 100%;
    margin: 0;
    padding: var(--spacing-sm);
  }

  .dashboard-container {
    padding: var(--spacing-sm);
  }

  .stats-card {
    margin: var(--spacing-sm) 0;
    padding: var(--spacing-md);
  }

  .stats-card .stat-info .title {
    font-size: var(--font-sm);
  }

  .stats-card .stat-info .count {
    font-size: var(--font-xl);
    padding: var(--spacing-sm) 0;
  }

  .stats-card .stat-info .stats-details {
    font-size: var(--font-xs);
  }

  .header-text {
    font-size: var(--font-lg);
    margin: var(--spacing-sm);
  }

  .graph-card {
    margin: var(--spacing-sm);
    width: calc(100% - 2 * var(--spacing-sm));
  }
}

.stats-card .stat-info .title {
  font-size: 1rem; /* 16px */

  letter-spacing: 1px;
  text-transform: capitalize;
  background-color: #f2f2f2;
}

.stats-card .stat-info .count {
  padding: 0.625rem 0; /* 10px */

  font-size: 1.5625rem; /* 25px */
  letter-spacing: 1px;
}

.stats-card .stat-info .stats-details {
  font-size: 0.75rem; /* 12px */
}

.compact-sidebar {
  height: 100%;
  width: 3.75rem; /* 60px */
  position: fixed;
  z-index: 999;
  box-shadow: 0px 3px 6px var(--shadow);
  background-color: #000080;
}

.compact-sidebar .menu {
  height: 3.125rem; /* 50px */
  width: 3.125rem; /* 50px */
  color: var(--primary);
  position: relative;
  cursor: pointer;
}

.compact-sidebar .sidebar-menu .top-section {
  margin-top: 5rem; /* 80px */
}

.compact-sidebar .sidebar-menu .bottom-section {
  margin-top: 10.625rem; /* 170px */
}
/* Top nav */

#topnav {
  background-color: var(--white);
  box-shadow: 0px 3px 6px var(--shadow);
  position: absolute;
  width: 100%;
  height: 60px;
  text-align: center;
}

/* styling navlist */

#topnav a {
  float: left;
  display: block;
  color: var(--primary);
  text-align: center;
  padding: 12px;

  text-decoration: none;
  font-size: 20px;
}

/* top nav right */
.topnav-right {
  float: right;
  display: flex;
}

.nav-link {
  margin: 0 0.3125rem;
  text-align: center;
}

.notification-icon {
  font-size: 1.25rem;

  position: relative;
  top: 0.8125rem;
  color: var(--primary);
}

.badge1 {
  position: relative;
}

.badge1[data-badge]:after {
  content: attr(data-badge);
  position: absolute;
  top: 0.75rem;
  left: 1.3125rem;

  font-size: 0.7em;
  background: #e9203b;
  color: var(--white);
  width: 1rem;
  height: 1rem;
  line-height: 1.125rem;
  border-radius: 50%;
  box-shadow: 0 0 1px var(--shawdow);
  text-align: center;
}

td {
  width: 24.375rem;

  height: 8.75rem;
}

.gps_ring {
  border: 0.1875rem solid red;
  height: 1.125rem;
  width: 1.125rem;
}

.setting-icon {
  font-size: 1.25rem;

  position: relative;
  top: 0.625rem;
  color: var(--primary);
}

.rowcard {
  display: flex;
  flex-wrap: wrap;
  margin-right: -4.75rem;
  margin-left: -0.9375rem;
}

.user-section {
  display: flex;
  min-width: 7.5rem;
  height: 3.75rem;
  position: relative;
  padding: 0.625rem;
  width: fit-content;
  border-radius: 0.3125rem;
  cursor: pointer;
}

.user-section .user-icon-section {
  background-color: var(--primary);
  color: var(--white);
  height: 2.5rem;
  width: 2.5rem;
  border-radius: 50%;
  position: relative;
}

.user-section .user-details {
  margin: 0 0.625rem;
}

.user-section .user-details .users {
  top: 0.1875rem;
}

.user-details .users .name {
  font-size: 1rem;
}

.user-details .users .title {
  font-size: 0.75rem;
}

.project-container {
  padding: 1.25rem;
}

.project-card {
  width: 30%;
}

.project-list {
  width: 70%;
}

@media only screen and (max-width: 48rem) {
  .project-card {
    width: 100%;
  }
  .project-list {
    width: 100%;
  }
}

.horizontal-card {
  padding: 5.625rem 1.25rem;
}

.horizontal-card .title {
  font-size: 10rem;
}

.horizontal-card .project-details {
  bottom: 1.25rem;
}

.project-details h5 {
  font-size: 1.5625rem;
}

.project-details p {
  font-size: 0.875rem;
}

.horizontal-card .project-image .img {
  width: 18.75rem;
  right: -9.375rem;
}

.mobile-card {
  width: 100%;
  background-color: rebeccapurple;
}

@media only screen and (max-width: 48rem) {
  .horizontal-card .project-image .img {
    display: none;
  }
  .horizontal-card {
    height: 1.875rem;
    border-radius: 0.625rem;
    padding: 2.5rem;
  }
  .horizontal-card .title {
    display: none;
  }
  .horizontal-card .project-details {
    bottom: 0;
  }
}

.stepper {
  padding: 1.25rem;
  margin-left: 12.5rem;
}

.stepper .title {
  font-size: 1.5625rem;
  letter-spacing: 0.0625rem;
}

.title .icon {
  position: relative;
  top: 0.25rem;
}

.stepper .steps {
  margin: 1.25rem 0;
}

.steps .custom-btn {
  margin: 0.625rem 1.25rem;
}

.steps .custom-btn .icon {
  top: -0.125rem;

  font-size: 1.5625rem;
  position: relative;
}

@media only screen and (max-width: 48rem) {
  .stepper {
    margin: 0;
  }
}

.prev,
.next {
  position: absolute;
  z-index: 1000;
  top: 0;
  bottom: 0;
  margin: auto;
}

.next {
  right: 0;
}

::ng-deep .mat-step-label-selected {
  font-size: 1.125rem !important;
  font-weight: 500;
}

::ng-deep .mat-step-label {
  font-size: 1rem;
}

.state-content {
  font-size: 1rem;
}

.example-margin {
  margin: 0 0.625rem;
}

.card {
  background-color: var(--white);
  border-radius: 0.625rem;
  border: none;
  position: relative;
  margin-bottom: 1.875rem;
  box-shadow:
    0 0.46875rem 2.1875rem rgba(90, 97, 105, 0.1),
    0 0.9375rem 1.40625rem rgba(90, 97, 105, 0.1),
    0 0.25rem 0.53125rem rgba(90, 97, 105, 0.12),
    0 0.125rem 0.1875rem rgba(90, 97, 105, 0.1);
}

.card .card-statistic-3 .card-icon-large .fas,
.card .card-statistic-3 .card-icon-large .far,
.card .card-statistic-3 .card-icon-large .fab,
.card .card-statistic-3 .card-icon-large .fal {
  font-size: 6.875rem;
}

.card .card-statistic-3 .card-icon {
  text-align: center;
  line-height: 3.125rem;
  margin-left: 0.9375rem;
  color: #000;
  position: absolute;
  right: -0.3125rem;
  top: 1.25rem;
  opacity: 0.1;
}

.mat-button {
  background-color: var(--primary);
  border: none;
  color: blue;
  padding: 0.125rem 1.625rem;

  font-size: 0.875rem;
  text-align: center;
  margin: 0.25rem 0.125rem;
  opacity: 1;
  transition: 0.3s;
}

.mat-button:hover {
  opacity: 1;
  background-color: var(--primary);
  border: none;
  color: var(--white);
  padding: 0.125rem 1.8125rem;

  font-size: 0.875rem;
  text-align: center;
  margin: 0.25rem 0.125rem;
  opacity: 0.6;
  transition: 0.3s;
}

.mat-tab-active {
  font-size: 2.5rem;
  text-align: center;
  background-color: red;
}

.graph-card {
  background-color: #f2f2f2;
  box-shadow: 0px 0.1875rem 0.375rem #77777730;
  border-radius: 0.625rem;
  margin: 1rem;
  width: fit-content;
  overflow: visible;
}

.header-text {
  font-weight: 600;
  font-size: 1.5rem;

  margin: 1rem;
  text-align: center;
  text-transform: uppercase;
}

.box-margin {
  padding: 0;
  margin: 0;
}

.box-head {
  font-size: 1.3rem;
  font-weight: 600;

  margin: 1rem;
}

.box-text {
  font-size: 1.5rem;
  font-weight: 500;
  margin: 0.5rem 1rem;
}

.box-sm {
  font-size: 1rem;
  font-weight: 500;

  margin: 0 1rem;
}

.stats-card {
  padding: 0.5rem;
  background-color: #f2f2f2;
}
.cm {
  font-weight: 600;
  font-size: 1rem;
  margin: 4rem;
  text-align: center;
}
.leaflet-pane img {
  position: absolute;
}
.dash-table-tooltip {
  background-color: var(--white);
  color: var(--white);
  width: 500px !important;
  max-width: 500px !important;
  z-index: 0;
  position: absolute;
  border: solid;
  border-color: var(--primary);
}

.display-flex {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
